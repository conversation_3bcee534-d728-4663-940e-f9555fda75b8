<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签汇总 - <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/homepage.css">
</head>
<body>
    <!-- 导航栏 -->
    <header class="w-full bg-white shadow-md fixed top-0 left-0 z-10" data-component="header"></header>

    <!-- 主要内容 -->
    <main class="container mt-24">
        <!-- 标签云 -->
        <div class="mb-12">
            <div id="tag-cloud" class="tag-cloud">
                <!-- 标签将通过JavaScript动态生成 -->
                <div class="text-center text-gray-500">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>正在加载标签...</p>
                </div>
            </div>
        </div>

        <!-- 筛选控制 -->
        <div class="mb-8 text-center" style="display:none;">
            <div class="inline-flex items-center space-x-4 bg-white rounded-lg p-4 shadow-md">
                <span class="text-gray-600">当前筛选：</span>
                <span id="current-filter" class="font-bold text-primary">全部文章</span>
                <button id="clear-filter" class="text-sm text-gray-500 hover:text-primary transition-colors" style="display: none;">
                    <i class="fas fa-times mr-1"></i>清除筛选
                </button>
            </div>
        </div>

        <!-- 文章列表 -->
        <div class="mb-16">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-left text-main mb-2">
                    <span id="articles-title">所有文章</span>
                </h2>
                <div class="text-left">
                    <span id="articles-count" class="text-base text-secondary font-context"></span>
                </div>
            </div>

            <!-- 使用iframe预览组件替代简单的文章卡片 -->
            <div data-component="iframe-previews" id="iframe-previews">
                <div class="text-center text-gray-500 py-12">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>正在加载文章...</p>
                </div>
            </div>
        </div>

        <!-- 分页控制 -->
        <div id="pagination-controls" class="text-center mt-12 mb-16" style="display: none;">
            <div class="pagination-wrapper">
                <button id="first-page" class="pagination-btn pagination-btn-disabled" disabled title="第一页">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button id="prev-page" class="pagination-btn pagination-btn-disabled" disabled title="上一页">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="page-info">
                    <span id="page-info" class="text-secondary font-context">第 1 页，共 1 页</span>
                </div>
                <button id="next-page" class="pagination-btn pagination-btn-disabled" disabled title="下一页">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button id="last-page" class="pagination-btn pagination-btn-disabled" disabled title="最后一页">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="footer" data-component="footer"></footer>

    <!-- 标签页面专用样式 -->
    <style>
        /* 标签页面布局优化 - 移除container覆盖，使用全局样式 */

        /* 标签云样式优化 */
        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            justify-content: center;
            align-items: center;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(104, 144, 148, 0.1);
        }

        .tag-item {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: rgba(104, 144, 148, 0.1);
            color: var(--color-text-main);
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(104, 144, 148, 0.2);
            font-family: 'Noto Sans SC', sans-serif;
        }

        .tag-item:hover {
            background: rgba(104, 144, 148, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(104, 144, 148, 0.15);
        }

        .tag-item.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        /* 文章标题区域样式 */
        #articles-title {
            color: var(--color-text-main);
            font-family: 'Noto Sans SC', sans-serif;
        }

        #articles-count {
            color: var(--color-text-secondary);
            font-family: 'Noto Sans SC', sans-serif;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .tag-cloud {
                padding: 1rem;
                gap: 0.5rem;
            }

            .tag-item {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }
        }
    </style>

    <!-- 标签页面专用脚本 -->
    <script>
        // 全局变量
        let allArticles = [];
        let filteredArticles = []; // 当前过滤后的文章
        let allTags = {};
        let tagLayers = {};
        const selectedTags = new Set();
        let currentFilter = null;
        const ALL_TAG = '全部';

        // 分页相关变量
        let currentPage = 1;
        const articlesPerPage = 20;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 等待主脚本加载完成后再初始化标签页面，增加重试机制
            const waitForMainScript = (retryCount = 0) => {
                if (typeof initializeIframePreviews === 'function' && typeof Logger !== 'undefined') {
                    // 主脚本已加载，开始初始化
                    initializeTagsPage();
                } else if (retryCount < 20) {
                    // 继续等待，最多等待2秒
                    setTimeout(() => {
                        waitForMainScript(retryCount + 1);
                    }, 100);
                } else {
                    // 超时后强制初始化
                    console.warn('主脚本加载超时，强制初始化标签页面');
                    initializeTagsPage();
                }
            };
            waitForMainScript();
        });

        // 初始化标签页面
        async function initializeTagsPage() {
            try {
                // 加载文章数据
                await loadArticlesData();

                // 生成标签云
                generateTagCloud();

                // 初始化过滤后的文章列表
                filteredArticles = [...allArticles];

                // 显示第一页文章
                displayPagedArticles();

                // 显示或隐藏分页控制
                const paginationControls = document.getElementById('pagination-controls');
                if (filteredArticles.length > articlesPerPage) {
                    paginationControls.style.display = 'block';
                } else {
                    paginationControls.style.display = 'none';
                }

                // 绑定事件
                bindEvents();

            } catch (error) {
                console.error('标签页面初始化失败:', error);
                showError('加载失败，请刷新页面重试');
            }
        }

        // 加载文章数据
        async function loadArticlesData() {
            try {
                const response = await fetch('../../cache/metadata.json');
                const metadata = await response.json();

                // 将数据转换为数组格式，并标记置顶状态（复用首页逻辑）
                const articles = Object.entries(metadata).map(([path, info]) => {
                    return {
                        title: info.title,
                        link: `../../${path}`, // 从 docs/tags/ 到文章的正确相对路径
                        src: `../../${path}`,  // 从 docs/tags/ 到文章的正确相对路径
                        date: info.date,
                        tags: info.tags || ['博客'],
                        pinned: info.pinned || false
                    };
                });

                // 只获取非置顶文章（标签页面不显示置顶文章）
                allArticles = articles.filter(article => !article.pinned);

                // 按日期倒序排列文章
                allArticles.sort((a, b) => new Date(b.date) - new Date(a.date));

                // 统计标签并记录层级
                allTags = {};
                tagLayers = {};
                allArticles.forEach(article => {
                    article.tags.forEach((tag, idx) => {
                        allTags[tag] = (allTags[tag] || 0) + 1;
                        if (!(tag in tagLayers) || idx < tagLayers[tag]) {
                            tagLayers[tag] = idx; // 记录最上层出现的位置
                        }
                    });
                });

                console.log('文章数据加载完成:', allArticles.length, '篇文章');
                console.log('标签统计:', allTags);

            } catch (error) {
                console.error('加载文章数据失败:', error);
                throw error;
            }
        }

        // 生成标签云
        function generateTagCloud() {
            const tagCloudContainer = document.getElementById('tag-cloud');

            // 构建带层级信息的数组
            const tagsArray = Object.entries(allTags).map(([tag, count]) => ({
                tag,
                count,
                layer: tagLayers[tag] ?? 999
            }));

            // 在最前面插入虚拟“全部”标签
            tagsArray.unshift({ tag: ALL_TAG, count: allArticles.length, layer: -1 });

            // 按层级（小值优先）-> 频次排序
            tagsArray.sort((a, b) => {
                if (a.layer !== b.layer) return a.layer - b.layer;
                return b.count - a.count;
            });

            const tagElements = tagsArray.map(({ tag, count }) => {
                const activeClass = (selectedTags.size === 0 && tag === ALL_TAG) || selectedTags.has(tag) ? 'active' : '';
                return `<span class="tag-item tag-size-3 ${activeClass}"
                             data-tag="${tag}"
                             data-count="${count}">
                            ${tag} (${count})
                        </span>`;
            }).join('');

            tagCloudContainer.innerHTML = tagElements;
        }

        // 显示分页文章
        function displayPagedArticles() {
            const startIndex = (currentPage - 1) * articlesPerPage;
            const endIndex = startIndex + articlesPerPage;
            const pageArticles = filteredArticles.slice(startIndex, endIndex);

            displayArticles(pageArticles);
            updatePaginationControls();
        }

        // 显示文章列表（完全复用widgets.js的iframe预览组件）
        function displayArticles(articles) {
            const container = document.getElementById('iframe-previews');
            const countElement = document.getElementById('articles-count');

            if (articles.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-gray-500 py-12">
                        <i class="fas fa-search text-4xl mb-4"></i>
                        <p class="text-lg">没有找到相关文章</p>
                    </div>
                `;
                countElement.textContent = '';
                return;
            }

            // 更新文章数量显示
            const totalCount = filteredArticles.length;
            const currentCount = articles.length;
            const startIndex = (currentPage - 1) * articlesPerPage + 1;
            const endIndex = Math.min(startIndex + currentCount - 1, totalCount);

            if (totalCount > articlesPerPage) {
                countElement.textContent = `(第 ${startIndex}-${endIndex} 篇，共 ${totalCount} 篇)`;
            } else {
                countElement.textContent = `(${totalCount} 篇)`;
            }

            // 设置iframe预览组件的数据，完全复用widgets.js
            container.setAttribute('data-previews', JSON.stringify(articles));

            // 直接调用widgets.js的初始化函数，增加重试机制
            const initializeWithRetry = (retryCount = 0) => {
                if (typeof initializeIframePreviews === 'function') {
                    initializeIframePreviews();
                } else if (retryCount < 10) {
                    // 如果函数还没加载，等待一下再试，最多重试10次
                    setTimeout(() => {
                        initializeWithRetry(retryCount + 1);
                    }, 100);
                } else {
                    console.error('initializeIframePreviews 函数加载失败');
                }
            };
            initializeWithRetry();
        }

        // 绑定事件
        function bindEvents() {
            // 标签点击事件（多选切换）
            document.getElementById('tag-cloud').addEventListener('click', function(e) {
                if (e.target.classList.contains('tag-item')) {
                    const tag = e.target.dataset.tag;
                    if (tag === ALL_TAG) {
                        selectedTags.clear(); // 清除所有已选标签
                    } else {
                        if (selectedTags.has(tag)) {
                            selectedTags.delete(tag);
                        } else {
                            selectedTags.add(tag);
                        }
                    }
                    // 重新渲染标签云和文章列表
                    generateTagCloud();
                    applyFilters();
                }
            });

            // 分页按钮事件
            document.getElementById('first-page').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage = 1;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });

            document.getElementById('prev-page').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });

            document.getElementById('next-page').addEventListener('click', function() {
                const totalPages = Math.ceil(filteredArticles.length / articlesPerPage);
                if (currentPage < totalPages) {
                    currentPage++;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });

            document.getElementById('last-page').addEventListener('click', function() {
                const totalPages = Math.ceil(filteredArticles.length / articlesPerPage);
                if (currentPage < totalPages) {
                    currentPage = totalPages;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });
        }

        // 应用筛选并显示文章
        function applyFilters() {
            // 重置到第一页
            currentPage = 1;

            if (selectedTags.size > 0) {
                filteredArticles = allArticles.filter(article => {
                    return article.tags.some(tag => selectedTags.has(tag));
                });
                document.getElementById('articles-title').textContent = '筛选后的文章';
            } else {
                filteredArticles = [...allArticles];
                document.getElementById('articles-title').textContent = '所有文章';
            }

            // 显示分页文章
            displayPagedArticles();

            // 显示或隐藏分页控制
            const paginationControls = document.getElementById('pagination-controls');
            if (filteredArticles.length > articlesPerPage) {
                paginationControls.style.display = 'block';
            } else {
                paginationControls.style.display = 'none';
            }
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('iframe-previews');
            container.innerHTML = `
                <div class="text-center text-red-500 py-12">
                    <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                    <p class="text-lg">${message}</p>
                </div>
            `;
        }

        // 更新分页控制
        function updatePaginationControls() {
            const totalPages = Math.ceil(filteredArticles.length / articlesPerPage);

            // 更新页面信息
            document.getElementById('page-info').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;

            // 获取所有按钮
            const firstBtn = document.getElementById('first-page');
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');
            const lastBtn = document.getElementById('last-page');

            // 更新按钮状态
            const isFirstPage = currentPage === 1;
            const isLastPage = currentPage === totalPages;

            firstBtn.disabled = isFirstPage;
            prevBtn.disabled = isFirstPage;
            nextBtn.disabled = isLastPage;
            lastBtn.disabled = isLastPage;

            // 更新按钮样式类
            firstBtn.className = isFirstPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
            prevBtn.className = isFirstPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
            nextBtn.className = isLastPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
            lastBtn.className = isLastPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
        }

        // 滚动到文章区域
        function scrollToArticlesSection() {
            const articlesSection = document.getElementById('iframe-previews');
            if (articlesSection) {
                // 使用平滑滚动，并留出一些顶部空间
                const offsetTop = articlesSection.offsetTop - 100;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        }

        // 生成标签颜色
        function generateTagColor(tag) {
            // 使用标签名生成一致的颜色
            let hash = 0;
            for (let i = 0; i < tag.length; i++) {
                hash = tag.charCodeAt(i) + ((hash << 5) - hash);
            }

            const hue = Math.abs(hash) % 360;
            return `hsl(${hue}, 65%, 55%)`;
        }
    </script>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>
