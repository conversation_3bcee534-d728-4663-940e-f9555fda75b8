<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签汇总 - <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../css/styles.css">
</head>
<body class="bg-background text-text-main">
    <!-- 导航栏 -->
    <header class="w-full bg-white shadow-md fixed top-0 left-0 z-10" data-component="header"></header>

    <!-- 主要内容 -->
    <main class="container mt-24">
        <!-- 标签云 -->
        <div class="mb-12">
            <div id="tag-cloud" class="tag-cloud">
                <!-- 标签将通过JavaScript动态生成 -->
                <div class="text-center text-gray-500">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>正在加载标签...</p>
                </div>
            </div>
        </div>

        <!-- 清除筛选按钮 -->
        <div class="mb-8 text-center">
            <button id="clear-filter" class="clear-filter-btn" style="display: none;">
                <i class="fas fa-times mr-1"></i>清除筛选
            </button>
        </div>

        <!-- 文章列表 -->
        <div class="mb-16">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-left text-main mb-2">
                    <span id="articles-title">所有文章</span>
                </h2>
                <div class="text-left">
                    <span id="articles-count" class="text-base text-secondary font-context"></span>
                </div>
            </div>

            <!-- 使用iframe预览组件替代简单的文章卡片 -->
            <div data-component="iframe-previews" id="iframe-previews">
                <div class="text-center text-gray-500 py-12">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>正在加载文章...</p>
                </div>
            </div>
        </div>

        <!-- 分页控制 -->
        <div id="article-controls" class="text-center mt-12 mb-16">
            <div id="pagination-controls" class="mt-8" style="display: none;">
                <div class="pagination-wrapper">
                    <button id="first-page" class="pagination-btn pagination-btn-disabled" disabled title="第一页">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button id="prev-page" class="pagination-btn pagination-btn-disabled" disabled title="上一页">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="page-info">
                        <span id="page-info" class="text-secondary font-context">第 1 页，共 1 页</span>
                    </div>
                    <button id="next-page" class="pagination-btn pagination-btn-disabled" disabled title="下一页">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button id="last-page" class="pagination-btn pagination-btn-disabled" disabled title="最后一页">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="footer" data-component="footer"></footer>

    <!-- 加载组件脚本 -->
    <script src="../../js/main.js"></script>
</body>
</html>
