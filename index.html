<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字海洋的月光</title>
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/homepage.css">
</head>
<body class="selection:bg-[#689094]/30 selection:text-[#3D3D3D]">
    <!-- 添加导航栏 -->
    <header class="w-full bg-white shadow-md fixed top-0 left-0 z-10" data-component="header"></header>

    <!-- 添加"鲸波月影"部分 -->
    <div id="whale-moon-title" class="w-full bg-[var(--color-background)] text-center py-16 mt-16">
        <h1 class="whale-moon-text text-6xl md:text-8xl text-gray-800">
            <span class="char" data-char="鲸">鲸</span>
            <span class="char" data-char="波">波</span>
            <span class="char" data-char="月">月</span>
            <span class="char" data-char="影">影</span>
        </h1>
    </div>

    <div id="hero-content" class="content-wrapper container">
        <div class="binary-bg" aria-hidden="true">
            <span>0</span><span>1</span><span>1</span><span>0</span><span>1</span><span>0</span><span>0</span><span>1</span>
            <span>1</span><span>0</span><span>0</span><span>1</span><span>0</span><span>1</span><span>1</span><span>0</span>
            <span>0</span><span>1</span><span>1</span><span>0</span><span>1</span><span>0</span><span>0</span><span>1</span>
        </div>

        <div class="mb-4 md:mb-6">
            <p class="text-xl md:text-3xl font-context text-secondary ocean-waves">
                在 <span class="text-2xl md:text-4xl text-tertiary wave-number">0</span> 与 <span class="text-2xl md:text-4xl text-tertiary wave-number">1</span> 的海洋
            </p>
        </div>

        <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-display">
            <span class="water-ripple">打捞</span>
            <br class="sm:hidden"> <!-- Break line on very small screens -->
            <span class="block mt-2 md:mt-3">
                <span class="text-highlight-gradient moonlight-fragments" data-text="月光">月光</span><span class="text-highlight-gradient moonlight-fragments" data-text="碎片">碎片</span>
            </span>
        </h1>

        <p class="text-sm md:text-md uppercase tracking-wider mt-8 md:mt-12 text-tertiary">
            SALVAGING MOONLIGHT IN A DIGITAL SEA
        </p>
    </div>

    <!-- 分页时的顶部间距占位 -->
    <div id="pagination-spacer" class="hidden" style="height: 120px; margin-top: 64px;"></div>

    <!-- 置顶文章预览组件 -->
    <div data-component="pinned-previews" id="pinned-previews"></div>

    <!-- 普通文章预览组件 -->
    <div data-component="iframe-previews" id="iframe-previews"></div>

    <!-- 显示更多按钮和分页控制 -->
    <div id="article-controls" class="text-center mt-12 mb-16" style="display: none;">
        <button id="show-all-btn" class="show-all-button">
            <span class="text-highlight-gradient font-context">显示所有文章</span>
        </button>
        <div id="pagination-controls" class="mt-8" style="display: none;">
            <div class="pagination-wrapper">
                <button id="first-page" class="pagination-btn pagination-btn-disabled" disabled title="第一页">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button id="prev-page" class="pagination-btn pagination-btn-disabled" disabled title="上一页">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="page-info">
                    <span id="page-info" class="text-secondary font-context">第 1 页，共 1 页</span>
                </div>
                <button id="next-page" class="pagination-btn pagination-btn-disabled" disabled title="下一页">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button id="last-page" class="pagination-btn pagination-btn-disabled" disabled title="最后一页">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    </div>



    <script>
        // 全局变量
        let allRegularArticles = [];
        let currentPage = 1;
        const articlesPerPage = 20;
        let showingAll = false;

        // 文章预览卡片动画函数
        function animatePreviewCards() {
            const previewItems = document.querySelectorAll('.preview-item');
            previewItems.forEach((item, index) => {
                // 移除之前的动画类，重置状态
                item.classList.remove('animate-in');

                setTimeout(() => {
                    item.classList.add('animate-in');
                }, index * 50); // 每个卡片延迟50ms出现
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 立即触发首页内容的渐入动画，与页面整体动画并行
            const whaleMoonTitle = document.getElementById('whale-moon-title');
            const heroContent = document.getElementById('hero-content');

            // 防止全局动画系统影响这些元素
            function protectFromGlobalAnimations() {
                [whaleMoonTitle, heroContent].forEach(element => {
                    if (element) {
                        // 移除可能被全局系统添加的动画类
                        element.classList.remove('scroll-fade', 'fade-in', 'visible');
                        // 移除子元素的动画类
                        element.querySelectorAll('.scroll-fade, .fade-in, .visible').forEach(child => {
                            child.classList.remove('scroll-fade', 'fade-in', 'visible');
                        });
                    }
                });
            }

            // 立即保护元素
            protectFromGlobalAnimations();

            // 触发自定义动画
            whaleMoonTitle.classList.add('show');
            heroContent.classList.add('show');

            // 标记动画准备就绪，让其他元素可以开始动画
            setTimeout(() => {
                document.body.classList.add('animations-ready');
                // 触发文章预览卡片的交错动画
                animatePreviewCards();
            }, 200); // 给首页顶部动画一点时间先开始

            // 延迟再次保护，防止后续的全局系统影响
            setTimeout(protectFromGlobalAnimations, 100);
            setTimeout(protectFromGlobalAnimations, 500);
            setTimeout(protectFromGlobalAnimations, 1000);

            // 从metadata.json文件中获取文章数据
            fetch('cache/metadata.json')
                .then(response => response.json())
                .then(data => {
                    // 将数据转换为数组格式，并标记置顶状态
                    const articles = Object.entries(data).map(([path, info]) => {
                        return {
                            title: info.title,
                            link: path,
                            src: path,
                            date: info.date,
                            tags: info.tags || ['博客'],
                            pinned: info.pinned || false
                        };
                    });

                    // 分离置顶文章和普通文章
                    const pinnedArticles = articles.filter(article => article.pinned).slice(0, 2);
                    allRegularArticles = articles.filter(article => !article.pinned);

                    // 按日期倒序排列普通文章
                    allRegularArticles.sort((a, b) => new Date(b.date) - new Date(a.date));

                    // 设置置顶预览组件的data-previews属性
                    const pinnedContainer = document.getElementById('pinned-previews');
                    if (pinnedArticles.length > 0) {
                        pinnedContainer.setAttribute('data-previews', JSON.stringify(pinnedArticles));
                        pinnedContainer.setAttribute('data-pinned', 'true');
                    }

                    // 初始显示前10篇普通文章
                    displayArticles(allRegularArticles.slice(0, 10));

                    // 如果文章数量超过10篇，显示"显示所有"按钮
                    if (allRegularArticles.length > 10) {
                        document.getElementById('article-controls').style.display = 'block';
                    }

                    // 初始化iframe预览组件 - 延迟确保模块已加载
                    setTimeout(() => {
                        if (typeof initializeIframePreviews === 'function') {
                            initializeIframePreviews();
                        } else {
                            console.log('等待模块加载...');
                            // 如果函数还没加载，再等一会儿
                            setTimeout(() => {
                                if (typeof initializeIframePreviews === 'function') {
                                    initializeIframePreviews();
                                } else {
                                    console.error('initializeIframePreviews 函数未找到');
                                }
                            }, 500);
                        }
                    }, 100);

                    // 绑定事件监听器
                    setupEventListeners();
                })
                .catch(error => console.error('加载文章数据失败:', error));
        });

        // 显示文章函数
        function displayArticles(articles) {
            const previewsContainer = document.getElementById('iframe-previews');
            previewsContainer.setAttribute('data-previews', JSON.stringify(articles));

            // 重新初始化iframe预览组件 - 确保函数已加载
            const initializePreviewsWithRetry = () => {
                if (typeof initializeIframePreviews === 'function') {
                    initializeIframePreviews();
                    // 快速触发动画，减少白屏时间
                    setTimeout(() => {
                        animatePreviewCards();
                    }, 50);
                } else {
                    // 如果函数还没加载，等待一下再试
                    setTimeout(initializePreviewsWithRetry, 100);
                }
            };

            initializePreviewsWithRetry();
        }

        // 设置事件监听器
        function setupEventListeners() {
            // "显示所有"按钮点击事件
            document.getElementById('show-all-btn').addEventListener('click', function() {
                showingAll = true;
                currentPage = 1;
                this.style.display = 'none';

                if (allRegularArticles.length > articlesPerPage) {
                    // 需要分页
                    document.getElementById('pagination-controls').style.display = 'block';
                    displayPagedArticles();
                } else {
                    // 不需要分页，直接显示所有
                    displayArticles(allRegularArticles);
                    // 确保在第一页显示标题、内容和置顶文章，隐藏占位元素
                    const whaleMoonTitle = document.getElementById('whale-moon-title');
                    const heroContent = document.getElementById('hero-content');
                    const pinnedContainer = document.getElementById('pinned-previews');
                    const paginationSpacer = document.getElementById('pagination-spacer');
                    whaleMoonTitle.classList.remove('hidden');
                    whaleMoonTitle.classList.add('show');
                    heroContent.classList.remove('hidden');
                    heroContent.classList.add('show');
                    pinnedContainer.style.display = 'block';
                    paginationSpacer.classList.add('hidden');
                }
            });

            // 分页按钮事件
            document.getElementById('first-page').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage = 1;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });

            document.getElementById('prev-page').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });

            document.getElementById('next-page').addEventListener('click', function() {
                const totalPages = Math.ceil(allRegularArticles.length / articlesPerPage);
                if (currentPage < totalPages) {
                    currentPage++;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });

            document.getElementById('last-page').addEventListener('click', function() {
                const totalPages = Math.ceil(allRegularArticles.length / articlesPerPage);
                if (currentPage < totalPages) {
                    currentPage = totalPages;
                    displayPagedArticles();
                    scrollToArticlesSection();
                }
            });
        }

        // 显示分页文章
        function displayPagedArticles() {
            const startIndex = (currentPage - 1) * articlesPerPage;
            const endIndex = startIndex + articlesPerPage;
            const pageArticles = allRegularArticles.slice(startIndex, endIndex);

            displayArticles(pageArticles);
            updatePaginationControls();

            // 控制"鲸波月影"标题、内容、置顶文章和占位元素的显示
            const whaleMoonTitle = document.getElementById('whale-moon-title');
            const heroContent = document.getElementById('hero-content');
            const pinnedContainer = document.getElementById('pinned-previews');
            const paginationSpacer = document.getElementById('pagination-spacer');

            if (currentPage > 1) {
                // 非首页时隐藏标题、内容和置顶文章，显示占位元素
                whaleMoonTitle.classList.add('hidden');
                whaleMoonTitle.classList.remove('show');
                heroContent.classList.add('hidden');
                heroContent.classList.remove('show');
                pinnedContainer.style.display = 'none';
                paginationSpacer.classList.remove('hidden');
            } else {
                // 首页时显示标题、内容和置顶文章，隐藏占位元素
                whaleMoonTitle.classList.remove('hidden');
                heroContent.classList.remove('hidden');
                pinnedContainer.style.display = 'block';
                paginationSpacer.classList.add('hidden');

                // 立即添加show类，触发Q弹动画
                whaleMoonTitle.classList.add('show');
                heroContent.classList.add('show');
            }
        }

        // 滚动到文章区域
        function scrollToArticlesSection() {
            const articlesSection = document.getElementById('iframe-previews');
            if (articlesSection) {
                // 使用平滑滚动，并留出一些顶部空间
                const offsetTop = articlesSection.offsetTop - 100;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        }

        // 更新分页控制
        function updatePaginationControls() {
            const totalPages = Math.ceil(allRegularArticles.length / articlesPerPage);

            // 更新页面信息
            document.getElementById('page-info').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;

            // 获取所有按钮
            const firstBtn = document.getElementById('first-page');
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');
            const lastBtn = document.getElementById('last-page');

            // 更新按钮状态
            const isFirstPage = currentPage === 1;
            const isLastPage = currentPage === totalPages;

            firstBtn.disabled = isFirstPage;
            prevBtn.disabled = isFirstPage;
            nextBtn.disabled = isLastPage;
            lastBtn.disabled = isLastPage;

            // 更新按钮样式类
            firstBtn.className = isFirstPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
            prevBtn.className = isFirstPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
            nextBtn.className = isLastPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
            lastBtn.className = isLastPage ? 'pagination-btn pagination-btn-disabled' : 'pagination-btn';
        }
    </script>

    <div class="mt-16">
        <p class="text-sm text-tertiary moonlight-shimmer">鲸波深处，代码与月光共振</p>
    </div>

    <!-- 页脚 -->
    <footer class="footer mt-12" data-component="footer"></footer>
    <!-- 加载组件脚本 -->
    <script src="js/main.js"></script>
</body>
</html>
